# -*- coding: utf-8 -*-
"""
Modern Dashboard Interface

This module provides a modern, always-accessible dashboard interface that replaces
the floating UI with better feature visibility and user experience.

Features:
- Modern card-based layout
- Quick access to all features
- Visual status indicators
- Responsive design
- Keyboard shortcuts
- Always-ready translation access
"""

import os
import sys
from typing import Optional, Dict, Any
from pathlib import Path

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QPushButton,
    QFrame, QScrollArea, QSizePolicy, QSpacerItem, QApplication, QSystemTrayIcon,
    QMenu, QMessageBox, QGraphicsDropShadowEffect
)
from PySide6.QtCore import Qt, Signal, Slot, QTimer, QPropertyAnimation, QEasingCurve, QRect
from PySide6.QtGui import QFont, QIcon, QPixmap, QPainter, QColor, QBrush, QPen, QKeySequence, QShortcut

from manager.config_manager import ConfigManager
from manager.cache_manager import CacheManager
from services.clipboard.clipboard_service import ClipboardService
from ui.settings_window import SettingsWindow
from ui.custom_result_window import CustomResultWindow
from ui.custom_prompt_window import CustomPromptWindow
from ui.widgets import StyledButton

class FeatureCard(QFrame):
    """Modern card widget for displaying features."""
    
    clicked = Signal()
    
    def __init__(self, title: str, description: str, icon_name: str = "", status: str = "ready"):
        super().__init__()
        self.title = title
        self.description = description
        self.icon_name = icon_name
        self.status = status
        
        self.setup_ui()
        self.setup_styling()
        
    def setup_ui(self):
        """Set up the card UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(12)
        
        # Header with icon and title
        header_layout = QHBoxLayout()
        
        # Icon (placeholder for now)
        self.icon_label = QLabel("🎯")  # Will be replaced with proper icons
        self.icon_label.setObjectName("FeatureCardIcon")
        self.icon_label.setFixedSize(32, 32)
        self.icon_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(self.icon_label)
        
        # Title and status
        title_layout = QVBoxLayout()
        title_layout.setSpacing(4)
        
        self.title_label = QLabel(self.title)
        self.title_label.setObjectName("FeatureCardTitle")
        title_layout.addWidget(self.title_label)
        
        self.status_label = QLabel(self.status.title())
        self.status_label.setObjectName("FeatureCardStatus")
        title_layout.addWidget(self.status_label)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # Description
        self.description_label = QLabel(self.description)
        self.description_label.setObjectName("FeatureCardDescription")
        self.description_label.setWordWrap(True)
        layout.addWidget(self.description_label)
        
        # Add stretch to push content to top
        layout.addStretch()
        
    def setup_styling(self):
        """Set up card styling."""
        self.setObjectName("FeatureCard")
        self.setCursor(Qt.PointingHandCursor)
        
        # Add drop shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 30))
        shadow.setOffset(0, 4)
        self.setGraphicsEffect(shadow)
        
        # Set status-based styling
        self.update_status_styling()
        
    def update_status_styling(self):
        """Update styling based on status."""
        status_colors = {
            "ready": "#cbd5e1",
            "active": "#22c55e", 
            "inactive": "#64748b",
            "error": "#ef4444"
        }
        
        color = status_colors.get(self.status, "#cbd5e1")
        self.status_label.setStyleSheet(f"color: {color}; font-weight: 600;")
        
    def set_status(self, status: str):
        """Update the card status."""
        self.status = status
        self.status_label.setText(status.title())
        self.update_status_styling()
        
    def mousePressEvent(self, event):
        """Handle mouse press events."""
        if event.button() == Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)

class QuickActionBar(QFrame):
    """Quick action bar for frequently used functions."""
    
    # Signals
    translate_text_requested = Signal(str)
    start_service_requested = Signal()
    stop_service_requested = Signal()
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the quick action bar UI."""
        self.setObjectName("QuickActionBar")
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(12)
        
        # Service status indicator
        self.service_status = QLabel("●")
        self.service_status.setObjectName("ServiceStatusIndicator")
        self.service_status.setFixedSize(16, 16)
        layout.addWidget(self.service_status)
        
        self.service_label = QLabel("Translation Service")
        self.service_label.setObjectName("ServiceLabel")
        layout.addWidget(self.service_label)
        
        layout.addStretch()
        
        # Quick translate button
        self.quick_translate_btn = StyledButton("Quick Translate", "primary")
        self.quick_translate_btn.setObjectName("QuickTranslateButton")
        layout.addWidget(self.quick_translate_btn)
        
        # Service toggle button
        self.service_toggle_btn = StyledButton("Start Service", "secondary")
        self.service_toggle_btn.setObjectName("ServiceToggleButton")
        layout.addWidget(self.service_toggle_btn)
        
        # Connect signals
        self.service_toggle_btn.clicked.connect(self.toggle_service)
        
    def toggle_service(self):
        """Toggle the translation service."""
        if self.service_toggle_btn.text() == "Start Service":
            self.start_service_requested.emit()
        else:
            self.stop_service_requested.emit()
            
    def update_service_status(self, is_running: bool):
        """Update service status display."""
        if is_running:
            self.service_status.setStyleSheet("color: #22c55e;")  # Green
            self.service_label.setText("Translation Service (Active)")
            self.service_toggle_btn.setText("Stop Service")
        else:
            self.service_status.setStyleSheet("color: #64748b;")  # Gray
            self.service_label.setText("Translation Service (Inactive)")
            self.service_toggle_btn.setText("Start Service")

class ModernDashboard(QWidget):
    """Modern dashboard interface for the translation application."""
    
    # Signals
    status_updated = Signal()
    request_translation_work = Signal(str)
    
    def __init__(self):
        super().__init__()
        
        # Core managers and services
        self.config_manager = ConfigManager(os.path.expanduser("~/.clipboard_translator_config.json"))
        self.cache_manager = CacheManager(os.path.expanduser("~/.clipboard_translator_cache.db"))
        self.clipboard_service = None
        
        # UI components
        self.prompt_window = None
        self.result_window = None
        self.settings_window = None
        
        # Audio windows
        self.audio_recording_window = None
        self.audio_transcription_window = None
        self.audio_playback_window = None
        
        self.setup_ui()
        self.setup_connections()
        self.setup_system_tray()
        self.setup_keyboard_shortcuts()
        
        # Initialize service status
        QTimer.singleShot(100, self.update_service_status_ui)
        
        # Auto-start if configured
        if self.config_manager.get('auto_start', False):
            QTimer.singleShot(100, self.start_service)
            
    def setup_ui(self):
        """Set up the main dashboard UI."""
        self.setWindowTitle("Translation Dashboard")
        self.setMinimumSize(800, 600)
        self.setObjectName("ModernDashboard")
        
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Header
        header = self.create_header()
        main_layout.addWidget(header)
        
        # Quick action bar
        self.quick_action_bar = QuickActionBar()
        main_layout.addWidget(self.quick_action_bar)
        
        # Main content area
        content_area = self.create_content_area()
        main_layout.addWidget(content_area)
        
    def create_header(self) -> QWidget:
        """Create the dashboard header."""
        header = QFrame()
        header.setObjectName("DashboardHeader")
        header.setFixedHeight(80)
        
        layout = QHBoxLayout(header)
        layout.setContentsMargins(24, 16, 24, 16)
        
        # Title and subtitle
        title_layout = QVBoxLayout()
        title_layout.setSpacing(4)
        
        title = QLabel("Translation Dashboard")
        title.setObjectName("MainWindowTitle")
        title_layout.addWidget(title)
        
        subtitle = QLabel("Always-ready translation and audio processing")
        subtitle.setObjectName("MainWindowSubtitle")
        title_layout.addWidget(subtitle)
        
        layout.addLayout(title_layout)
        layout.addStretch()
        
        # Header actions
        settings_btn = StyledButton("Settings", "ghost")
        settings_btn.clicked.connect(self.open_settings)
        layout.addWidget(settings_btn)
        
        return header
        
    def create_content_area(self) -> QWidget:
        """Create the main content area with feature cards."""
        # Scroll area for responsive design
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setObjectName("ContentScrollArea")
        
        # Content widget
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(24, 24, 24, 24)
        content_layout.setSpacing(24)
        
        # Feature cards grid
        cards_grid = self.create_feature_cards()
        content_layout.addWidget(cards_grid)
        
        # Recent activity section
        recent_section = self.create_recent_activity_section()
        content_layout.addWidget(recent_section)
        
        content_layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        return scroll_area

    def create_feature_cards(self) -> QWidget:
        """Create the feature cards grid."""
        cards_container = QFrame()
        cards_container.setObjectName("FeatureCardsContainer")

        grid_layout = QGridLayout(cards_container)
        grid_layout.setSpacing(16)
        grid_layout.setContentsMargins(0, 0, 0, 0)

        # Translation features
        translation_card = FeatureCard(
            "Text Translation",
            "Instantly translate clipboard content with AI-powered translation services",
            "translate",
            "ready"
        )
        translation_card.clicked.connect(self.open_translation_interface)
        grid_layout.addWidget(translation_card, 0, 0)

        # Audio recording
        recording_card = FeatureCard(
            "Audio Recording",
            "Record system audio or microphone input with custom naming and locations",
            "record",
            "ready"
        )
        recording_card.clicked.connect(self.open_audio_recording)
        grid_layout.addWidget(recording_card, 0, 1)

        # Audio transcription
        transcription_card = FeatureCard(
            "Audio Transcription",
            "Convert speech to text using local STT models with segment selection",
            "transcribe",
            "ready"
        )
        transcription_card.clicked.connect(self.open_audio_transcription)
        grid_layout.addWidget(transcription_card, 1, 0)

        # Audio playback
        playback_card = FeatureCard(
            "Audio Playback",
            "Play audio with synchronized transcription display and auto-pause features",
            "playback",
            "ready"
        )
        playback_card.clicked.connect(self.open_audio_playback)
        grid_layout.addWidget(playback_card, 1, 1)

        # Settings and management
        settings_card = FeatureCard(
            "Settings & Cache",
            "Configure translation services, manage cache, and customize preferences",
            "settings",
            "ready"
        )
        settings_card.clicked.connect(self.open_settings)
        grid_layout.addWidget(settings_card, 2, 0)

        # Help and documentation
        help_card = FeatureCard(
            "Help & Documentation",
            "Access user guides, keyboard shortcuts, and troubleshooting information",
            "help",
            "ready"
        )
        help_card.clicked.connect(self.show_help)
        grid_layout.addWidget(help_card, 2, 1)

        return cards_container

    def create_recent_activity_section(self) -> QWidget:
        """Create the recent activity section."""
        section = QFrame()
        section.setObjectName("RecentActivitySection")

        layout = QVBoxLayout(section)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(16)

        # Section title
        title = QLabel("Recent Activity")
        title.setObjectName("SectionTitle")
        layout.addWidget(title)

        # Activity list placeholder
        activity_frame = QFrame()
        activity_frame.setObjectName("ActivityFrame")
        activity_frame.setMinimumHeight(200)

        activity_layout = QVBoxLayout(activity_frame)
        activity_layout.setContentsMargins(20, 20, 20, 20)

        placeholder = QLabel("Recent translations and audio processing will appear here")
        placeholder.setObjectName("ActivityPlaceholder")
        placeholder.setAlignment(Qt.AlignCenter)
        activity_layout.addWidget(placeholder)

        layout.addWidget(activity_frame)

        return section

    def setup_connections(self):
        """Set up signal connections."""
        # Quick action bar connections
        self.quick_action_bar.start_service_requested.connect(self.start_service)
        self.quick_action_bar.stop_service_requested.connect(self.stop_service)

        # Status update connection
        self.status_updated.connect(self.update_service_status_ui)

    def setup_system_tray(self):
        """Set up system tray functionality."""
        if not QSystemTrayIcon.isSystemTrayAvailable():
            return

        self.tray_icon = QSystemTrayIcon(self)
        # Create a simple colored icon
        pixmap = QPixmap(16, 16)
        pixmap.fill(QColor(14, 165, 233))  # Blue color
        painter = QPainter(pixmap)
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.drawEllipse(4, 4, 8, 8)
        painter.end()
        self.tray_icon.setIcon(QIcon(pixmap))

        # Tray menu
        tray_menu = QMenu()

        show_action = tray_menu.addAction("Show Dashboard")
        show_action.triggered.connect(self.show_and_raise)

        tray_menu.addSeparator()

        start_action = tray_menu.addAction("Start Service")
        start_action.triggered.connect(self.start_service)

        stop_action = tray_menu.addAction("Stop Service")
        stop_action.triggered.connect(self.stop_service)

        tray_menu.addSeparator()

        quit_action = tray_menu.addAction("Quit")
        quit_action.triggered.connect(QApplication.instance().quit)

        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.show()

        # Tray icon click
        self.tray_icon.activated.connect(self.on_tray_activated)

    def setup_keyboard_shortcuts(self):
        """Set up keyboard shortcuts."""
        # Global shortcuts for quick access
        self.quick_translate_shortcut = QShortcut(QKeySequence("Ctrl+Shift+T"), self)
        self.quick_translate_shortcut.activated.connect(self.quick_translate)

        self.show_dashboard_shortcut = QShortcut(QKeySequence("Ctrl+Shift+D"), self)
        self.show_dashboard_shortcut.activated.connect(self.show_and_raise)

        self.toggle_service_shortcut = QShortcut(QKeySequence("Ctrl+Shift+S"), self)
        self.toggle_service_shortcut.activated.connect(self.toggle_service)

    # === Service Management ===
    def start_service(self):
        """Start the clipboard translation service."""
        if self.clipboard_service and self.clipboard_service.isRunning():
            return

        try:
            self.clipboard_service = ClipboardService(self.config_manager, self.cache_manager)
            self.request_translation_work.connect(self.clipboard_service.do_translation_work)
            self.clipboard_service.translation_requested.connect(self.show_translation_prompt)
            self.clipboard_service.translation_done.connect(self.show_translation_result)
            self.clipboard_service.error_occurred.connect(self.show_error_message)
            self.clipboard_service.finished.connect(self.on_service_finished)
            self.clipboard_service.start()

            self.status_updated.emit()
            self.tray_icon.showMessage(
                "Service Started",
                "Translation service is now active",
                QSystemTrayIcon.Information,
                2000
            )

        except Exception as e:
            QMessageBox.critical(self, "Service Error", f"Failed to start service: {e}")

    def stop_service(self):
        """Stop the clipboard translation service."""
        if not (self.clipboard_service and self.clipboard_service.isRunning()):
            return

        self.clipboard_service.stop()

    def toggle_service(self):
        """Toggle the translation service."""
        if self.clipboard_service and self.clipboard_service.isRunning():
            self.stop_service()
        else:
            self.start_service()

    def on_service_finished(self):
        """Handle service finished."""
        self.clipboard_service.deleteLater()
        self.clipboard_service = None
        self.status_updated.emit()
        self.tray_icon.showMessage(
            "Service Stopped",
            "Translation service has been stopped",
            QSystemTrayIcon.Information,
            2000
        )

    @Slot()
    def update_service_status_ui(self):
        """Update service status in the UI."""
        is_running = bool(self.clipboard_service and self.clipboard_service.isRunning())
        self.quick_action_bar.update_service_status(is_running)

    # === UI Actions ===
    def show_and_raise(self):
        """Show and raise the dashboard window."""
        self.show()
        self.raise_()
        self.activateWindow()

    def on_tray_activated(self, reason):
        """Handle tray icon activation."""
        if reason == QSystemTrayIcon.DoubleClick:
            self.show_and_raise()

    def quick_translate(self):
        """Quick translate from clipboard."""
        if not (self.clipboard_service and self.clipboard_service.isRunning()):
            self.start_service()
            QTimer.singleShot(1000, self._do_quick_translate)
        else:
            self._do_quick_translate()

    def _do_quick_translate(self):
        """Perform quick translation."""
        try:
            import pyperclip
            text = pyperclip.paste()
            if text.strip():
                self.request_translation_work.emit(text)
            else:
                self.tray_icon.showMessage(
                    "No Text",
                    "No text found in clipboard",
                    QSystemTrayIcon.Warning,
                    2000
                )
        except Exception as e:
            self.show_error_message(f"Quick translate failed: {e}")

    # === Feature Actions ===
    def open_translation_interface(self):
        """Open the main translation interface."""
        # For now, just show a message - could open a dedicated translation window
        QMessageBox.information(
            self, "Translation Interface",
            "Translation service is available through clipboard monitoring.\n\n"
            "Copy text to clipboard and the service will automatically detect and translate it."
        )

    def open_audio_recording(self):
        """Open the audio recording window."""
        if self.audio_recording_window is None:
            from ui.audio_recording_window import AudioRecordingWindow
            self.audio_recording_window = AudioRecordingWindow()
            self.audio_recording_window.window_closed.connect(self.on_audio_recording_closed)
            self.audio_recording_window.recording_completed.connect(self.on_recording_completed)

        self.audio_recording_window.show()
        self.audio_recording_window.raise_()
        self.audio_recording_window.activateWindow()

    def open_audio_transcription(self):
        """Open the enhanced audio transcription window."""
        if self.audio_transcription_window is None:
            from ui.components.enhanced_audio_wave_visualizer import EnhancedAudioWaveVisualizer
            self.audio_transcription_window = EnhancedAudioWaveVisualizer()
            self.audio_transcription_window.windowClosed.connect(self.on_audio_transcription_closed)
            self.audio_transcription_window.transcriptionCompleted.connect(self.on_transcription_completed)

        self.audio_transcription_window.show()
        self.audio_transcription_window.raise_()
        self.audio_transcription_window.activateWindow()

    def open_audio_playback(self):
        """Open the audio playback window."""
        if self.audio_playback_window is None:
            from ui.audio_playback_window import AudioPlaybackWindow
            self.audio_playback_window = AudioPlaybackWindow()
            self.audio_playback_window.window_closed.connect(self.on_audio_playback_closed)
            self.audio_playback_window.transcription_requested.connect(self.on_transcription_requested)

        self.audio_playback_window.show()
        self.audio_playback_window.raise_()
        self.audio_playback_window.activateWindow()

    def open_settings(self):
        """Open the settings window."""
        if self.settings_window is None:
            self.settings_window = SettingsWindow(self.config_manager)

        self.settings_window.show()
        self.settings_window.raise_()
        self.settings_window.activateWindow()

    def show_help(self):
        """Show help and documentation."""
        help_text = """
        <h3>Translation Dashboard Help</h3>

        <h4>Keyboard Shortcuts:</h4>
        <ul>
        <li><b>Ctrl+Shift+T</b> - Quick translate from clipboard</li>
        <li><b>Ctrl+Shift+D</b> - Show dashboard</li>
        <li><b>Ctrl+Shift+S</b> - Toggle translation service</li>
        </ul>

        <h4>Features:</h4>
        <ul>
        <li><b>Text Translation</b> - Automatic clipboard monitoring and translation</li>
        <li><b>Audio Recording</b> - Record system audio or microphone</li>
        <li><b>Audio Transcription</b> - Convert speech to text with segment selection</li>
        <li><b>Audio Playback</b> - Synchronized transcription display</li>
        </ul>

        <h4>Getting Started:</h4>
        <ol>
        <li>Start the translation service using the toggle button</li>
        <li>Copy text to automatically trigger translation</li>
        <li>Use audio features for speech processing</li>
        <li>Access settings to configure services</li>
        </ol>
        """

        QMessageBox.information(self, "Help & Documentation", help_text)

    # === Audio Window Management ===
    def on_audio_recording_closed(self):
        """Handle audio recording window closed."""
        self.audio_recording_window = None

    def on_audio_transcription_closed(self):
        """Handle audio transcription window closed."""
        self.audio_transcription_window = None

    def on_audio_playback_closed(self):
        """Handle audio playback window closed."""
        self.audio_playback_window = None

    def on_recording_completed(self, file_path: str):
        """Handle recording completion."""
        self.tray_icon.showMessage(
            "Recording Complete",
            f"Audio saved to: {Path(file_path).name}",
            QSystemTrayIcon.Information,
            3000
        )

    def on_transcription_completed(self, result):
        """Handle transcription completion."""
        self.tray_icon.showMessage(
            "Transcription Complete",
            "Audio segment transcribed successfully",
            QSystemTrayIcon.Information,
            2000
        )

    def on_transcription_requested(self, start_time: float, end_time: float):
        """Handle transcription request from playback."""
        if self.audio_transcription_window is None:
            self.open_audio_transcription()
        else:
            self.audio_transcription_window.show()
            self.audio_transcription_window.raise_()
            self.audio_transcription_window.activateWindow()

    # === Translation UI Handlers ===
    @Slot(str)
    def show_translation_prompt(self, text: str):
        """Show translation prompt."""
        cached_translation = self.cache_manager.get_cached_translation(text)
        cache_info = " (Cached)" if cached_translation else ""

        self.prompt_window = CustomPromptWindow(text, cache_info)
        self.prompt_window.user_responded.connect(self.on_prompt_response)

        # Center on screen
        screen_center = QApplication.primaryScreen().geometry().center()
        self.prompt_window.move(screen_center - self.prompt_window.rect().center())
        self.prompt_window.show()

    def on_prompt_response(self, translate: bool):
        """Handle prompt response."""
        if translate:
            text_to_translate = self.prompt_window.text_to_translate
            if self.clipboard_service and self.clipboard_service.isRunning():
                self.request_translation_work.emit(text_to_translate)

        if self.prompt_window:
            self.prompt_window.close()
            self.prompt_window = None

    @Slot(str, str, bool, str)
    def show_translation_result(self, original: str, translation: str, from_cache: bool, structured_json: str):
        """Show translation result."""
        self.result_window = CustomResultWindow(original, translation, from_cache, structured_json)
        self.result_window.show()

        self.tray_icon.showMessage(
            "Translation Complete",
            f"Translated: {original[:30]}...",
            QSystemTrayIcon.Information,
            3000
        )

    @Slot(str)
    def show_error_message(self, message: str):
        """Show error message."""
        QMessageBox.critical(self, "Translation Error", message)

    # === Window Management ===
    def closeEvent(self, event):
        """Handle close event."""
        # Hide to tray instead of closing
        event.ignore()
        self.hide()

        self.tray_icon.showMessage(
            "Dashboard Hidden",
            "Application is still running in system tray",
            QSystemTrayIcon.Information,
            2000
        )

    def cleanup(self):
        """Clean up resources."""
        if self.clipboard_service and self.clipboard_service.isRunning():
            self.stop_service()
            self.clipboard_service.wait(1000)

        if hasattr(self, 'tray_icon'):
            self.tray_icon.hide()
