import sys
import math
import random
import numpy as np
from typing import List, <PERSON><PERSON>, Union
from PySide6.QtWidgets import (
    QApplication,
    QMainWindow,
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QPushButton,
    QLabel,
    QFrame,
    QGridLayout,
    QSizePolicy,
    QFileDialog,
    QMessageBox,
    QListWidget,
    QListWidgetItem,
    QSplitter,
    QScrollArea,
    QAbstractItemView,
    QStyle,
    QStyledItemDelegate
)
from PySide6.QtCore import (
    Qt,
    QTimer,
    Signal,
    Property,
    QSize,
    QPointF,
    QThread,
)
from PySide6.QtGui import (
    QPainter,
    QColor,
    QLinearGradient,
    QPen,
    QBrush,
    QFont,
    QPainterPath,
    QTextDocument
)

# Try to import pydub for audio processing
try:
    from pydub import AudioSegment

    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False
    print("Warning: pydub not available. Audio file loading disabled.")

# Try to import scipy for signal processing
try:
    from scipy.io import wavfile
    from scipy import signal

    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    print("Warning: scipy not available. WAV file loading disabled.")


class AudioLoaderThread(QThread):
    """Thread for loading audio files without blocking the UI"""

    audioLoaded = Signal(object, object, object)  # audio_data, duration, sample_rate
    errorOccurred = Signal(str)

    def __init__(self, file_path, parent=None):
        super().__init__(parent)
        self.file_path = file_path

    def run(self):
        try:
            if PYDUB_AVAILABLE:
                # Use pydub for various audio formats
                audio = AudioSegment.from_file(self.file_path)

                # Convert to mono
                if audio.channels > 1:
                    audio = audio.set_channels(1)

                # Get sample data as numpy array
                samples = np.array(audio.get_array_of_samples())

                # Normalize to [-1.0, 1.0]
                if audio.sample_width == 2:
                    samples = samples / 32768.0
                elif audio.sample_width == 4:
                    samples = samples / 2147483648.0

                # Downsample for visualization (max 2000 points)
                if len(samples) > 2000:
                    # Instead of simple resampling, we'll compute the envelope
                    samples = self.compute_waveform_envelope(samples, 2000)

                duration = len(audio) / 1000.0  # duration in seconds
                sample_rate = audio.frame_rate

                self.audioLoaded.emit(samples, duration, sample_rate)

            elif SCIPY_AVAILABLE and self.file_path.lower().endswith(".wav"):
                # Use scipy for WAV files as fallback
                sample_rate, samples = wavfile.read(self.file_path)

                # Convert to mono if needed
                if len(samples.shape) > 1:
                    samples = np.mean(samples, axis=1)

                # Normalize to [-1.0, 1.0]
                if samples.dtype == np.int16:
                    samples = samples / 32768.0
                elif samples.dtype == np.int32:
                    samples = samples / 2147483648.0
                elif samples.dtype == np.uint8:
                    samples = (samples - 128) / 128.0

                # Downsample for visualization (max 2000 points)
                if len(samples) > 2000:
                    samples = self.compute_waveform_envelope(samples, 2000)

                duration = len(samples) / sample_rate

                self.audioLoaded.emit(samples, duration, sample_rate)

            else:
                self.errorOccurred.emit(
                    "No audio processing library available. Install pydub or scipy."
                )

        except Exception as e:
            self.errorOccurred.emit(f"Error loading audio file: {str(e)}")

    def compute_waveform_envelope(self, samples, num_points):
        """Compute the envelope of the waveform for better visualization"""
        # Calculate the number of samples per segment
        segment_size = len(samples) // num_points

        # Initialize the envelope array
        envelope = np.zeros(num_points)

        for i in range(num_points):
            start_idx = i * segment_size
            end_idx = min((i + 1) * segment_size, len(samples))

            if start_idx >= len(samples):
                break

            # Get the segment
            segment = samples[start_idx:end_idx]

            # Calculate the RMS (root mean square) of the segment
            rms = np.sqrt(np.mean(segment**2))

            # Scale the RMS to get a good visualization
            envelope[i] = rms * 3.0  # Scale factor for better visualization

        return envelope


class RangeSlider(QWidget):
    """Custom range slider with two handles for selecting start and end positions"""

    rangeChanged = Signal(float, float)  # Emitted when the range changes

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumHeight(40)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        # Default values
        self._min_value = 0.0
        self._max_value = 1.0
        self._start_value = 0.0
        self._end_value = 1.0
        self._minimum_range = 0.1  # Minimum range between start and end

        # Handle properties
        self._handle_radius = 10
        self._handle_height = 20
        self._handle_width = 20

        # Appearance properties
        self._groove_color = QColor(71, 85, 105)  # #475569
        self._groove_height = 8
        self._handle_color = QColor(96, 165, 250)  # #60a5fa
        self._handle_border_color = QColor(30, 41, 59)  # #1e293b
        self._selection_color = QColor(99, 102, 241, 51)  # rgba(99, 102, 241, 0.2)

        # Interaction state
        self._dragging = None  # None, 'start', or 'end'
        self._hover = None  # None, 'start', or 'end'

        # Enable mouse tracking for hover effects
        self.setMouseTracking(True)

    def setRange(self, min_val, max_val):
        """Set the minimum and maximum values for the slider"""
        self._min_value = min_val
        self._max_value = max_val
        self.update()

    def setValues(self, start_val, end_val):
        """Set the start and end values"""
        # Ensure values are within range
        start_val = max(self._min_value, min(start_val, self._max_value))
        end_val = max(self._min_value, min(end_val, self._max_value))

        # Ensure minimum range
        if end_val - start_val < self._minimum_range:
            if start_val == self._start_value:
                end_val = start_val + self._minimum_range
            else:
                start_val = end_val - self._minimum_range

        # Ensure values are still within range after adjusting
        start_val = max(
            self._min_value, min(start_val, self._max_value - self._minimum_range)
        )
        end_val = max(
            self._min_value + self._minimum_range, min(end_val, self._max_value)
        )

        self._start_value = start_val
        self._end_value = end_val
        self.update()

    def values(self):
        """Get the current start and end values"""
        return (self._start_value, self._end_value)

    def paintEvent(self, event):
        """Draw the range slider"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Get widget dimensions
        width = self.width()
        height = self.height()

        # Calculate positions
        groove_y = height // 2 - self._groove_height // 2
        start_x = self._position_to_x(self._start_value)
        end_x = self._position_to_x(self._end_value)

        # Draw the groove
        painter.fillRect(0, groove_y, width, self._groove_height, self._groove_color)

        # Draw the selection
        painter.fillRect(
            start_x,
            groove_y,
            end_x - start_x,
            self._groove_height,
            self._selection_color,
        )

        # Draw the handles
        self._draw_handle(painter, start_x, height // 2, "start")
        self._draw_handle(painter, end_x, height // 2, "end")

    def _draw_handle(self, painter, x, y, handle_type):
        """Draw a slider handle"""
        # Set handle color based on state
        if self._dragging == handle_type:
            color = self._handle_color.darker(110)
        elif self._hover == handle_type:
            color = self._handle_color.lighter(110)
        else:
            color = self._handle_color

        # Draw handle
        painter.setBrush(QBrush(color))
        painter.setPen(QPen(self._handle_border_color, 2))

        # Draw as a circle
        painter.drawEllipse(QPointF(x, y), self._handle_radius, self._handle_radius)

    def _position_to_x(self, position):
        """Convert a position value to an x coordinate"""
        ratio = (position - self._min_value) / (self._max_value - self._min_value)
        return int(ratio * self.width())

    def _x_to_position(self, x):
        """Convert an x coordinate to a position value"""
        ratio = x / self.width()
        return self._min_value + ratio * (self._max_value - self._min_value)

    def mousePressEvent(self, event):
        """Handle mouse press events"""
        if event.button() == Qt.LeftButton:
            x = event.x()
            start_x = self._position_to_x(self._start_value)
            end_x = self._position_to_x(self._end_value)

            # Check which handle was clicked
            if abs(x - start_x) <= self._handle_radius:
                self._dragging = "start"
            elif abs(x - end_x) <= self._handle_radius:
                self._dragging = "end"

    def mouseMoveEvent(self, event):
        """Handle mouse move events"""
        x = event.x()
        start_x = self._position_to_x(self._start_value)
        end_x = self._position_to_x(self._end_value)

        # Update hover state
        if abs(x - start_x) <= self._handle_radius:
            if self._hover != "start":
                self._hover = "start"
                self.update()
        elif abs(x - end_x) <= self._handle_radius:
            if self._hover != "end":
                self._hover = "end"
                self.update()
        else:
            if self._hover is not None:
                self._hover = None
                self.update()

        # Handle dragging
        if self._dragging:
            position = self._x_to_position(x)

            if self._dragging == "start":
                # Dragging the start handle
                new_start = max(
                    self._min_value,
                    min(position, self._end_value - self._minimum_range),
                )
                self._start_value = new_start
            else:  # dragging == 'end'
                # Dragging the end handle
                new_end = max(
                    self._start_value + self._minimum_range,
                    min(position, self._max_value),
                )
                self._end_value = new_end

            self.update()
            self.rangeChanged.emit(self._start_value, self._end_value)

    def mouseReleaseEvent(self, event):
        """Handle mouse release events"""
        if event.button() == Qt.LeftButton:
            self._dragging = None

    def leaveEvent(self, event):
        """Handle mouse leave events"""
        self._hover = None
        self.update()


class WaveformCanvas(QWidget):
    """Custom widget for drawing the audio waveform and selection range"""

    positionChanged = Signal(float)  # Signal for position changes during playback

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumSize(400, 150)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        # Data properties
        self.audio_data: List[float] = []
        self.selected_range: Tuple[float, float] = (0.0, 1.0)  # (start, end)
        self.is_playing: bool = False
        self.playback_position: float = 0.0  # 0.0 to 1.0
        self.audio_duration: float = 1.0  # duration in seconds

        # Transcribed segments (start, end, text)
        self.transcribed_segments: List[Tuple[float, float, str]] = []

        # Appearance properties
        self.waveform_color = QColor(96, 165, 250)  # #60a5fa
        self.selection_color = QColor(99, 102, 241, 51)  # rgba(99, 102, 241, 0.2)
        self.selection_border_color = QColor(129, 140, 248)  # #818cf8
        self.playhead_color = QColor(239, 68, 68)  # #ef4444
        self.transcribed_color = QColor(16, 185, 129, 100)  # rgba(16, 185, 129, 0.4)
        self.transcribed_border_color = QColor(5, 150, 105)  # #059669
        self.bg_start_color = QColor(30, 41, 59)  # #1e293b
        self.bg_end_color = QColor(15, 23, 42)  # #0f172a

        # Animation timer
        self.animation_timer = QTimer(self)
        self.animation_timer.timeout.connect(self.update_playback_position)
        self.animation_timer.setInterval(16)  # ~60fps

    def set_audio_data(
        self, data: Union[List[float], np.ndarray], duration: float = 1.0
    ):
        """Set the audio data to visualize"""
        if isinstance(data, np.ndarray):
            self.audio_data = data.tolist()
        else:
            self.audio_data = data
        self.audio_duration = duration
        self.update()

    def set_selected_range(self, start: float, end: float):
        """Set the selected range (0.0 to 1.0)"""
        self.selected_range = (max(0.0, min(1.0, start)), max(0.0, min(1.0, end)))
        self.update()

    def set_playing(self, playing: bool):
        """Set the playing state"""
        self.is_playing = playing
        if playing:
            self.playback_position = self.selected_range[0]
            self.animation_timer.start()
        else:
            self.animation_timer.stop()
        self.update()

    def update_playback_position(self):
        """Update the playback position for animation"""
        if not self.is_playing:
            return

        # Calculate the duration of the selected range in seconds
        selection_duration = (
            self.selected_range[1] - self.selected_range[0]
        ) * self.audio_duration

        # Update position (16ms per frame)
        self.playback_position += 0.016 / selection_duration

        # Loop back to start if we reach the end
        if self.playback_position >= self.selected_range[1]:
            self.playback_position = self.selected_range[0]

        # Emit signal with normalized position
        self.positionChanged.emit(self.playback_position)
        self.update()

    def add_transcribed_segment(self, start: float, end: float, text: str):
        """Add a transcribed segment to the visualization"""
        self.transcribed_segments.append((start, end, text))
        self.update()

    def remove_transcribed_segment(self, index: int):
        """Remove a transcribed segment by index"""
        if 0 <= index < len(self.transcribed_segments):
            self.transcribed_segments.pop(index)
            self.update()

    def get_transcribed_segments(self):
        """Get all transcribed segments"""
        return self.transcribed_segments

    def is_segment_transcribed(self, start: float, end: float):
        """Check if a segment overlaps with any transcribed segment"""
        for t_start, t_end, _ in self.transcribed_segments:
            # Check for overlap
            if not (end <= t_start or start >= t_end):
                return True
        return False

    def paintEvent(self, event):
        """Draw the waveform, selection, transcribed segments, and playhead"""
        if not self.audio_data:
            return

        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        width = self.width()
        height = self.height()

        # Draw background gradient
        gradient = QLinearGradient(0, 0, 0, height)
        gradient.setColorAt(0, self.bg_start_color)
        gradient.setColorAt(1, self.bg_end_color)
        painter.fillRect(0, 0, width, height, gradient)

        # Draw waveform
        if self.audio_data:
            pen = QPen(self.waveform_color, 2)
            painter.setPen(pen)

            # Draw the waveform as a line
            path = QPainterPath()
            for i, value in enumerate(self.audio_data):
                x = (i / (len(self.audio_data) - 1)) * width
                # Scale the value to fit the canvas height
                y = height / 2 - value * (height * 0.4)

                if i == 0:
                    path.moveTo(x, y)
                else:
                    path.lineTo(x, y)

            painter.drawPath(path)

        # Draw transcribed segments
        for start, end, _ in self.transcribed_segments:
            start_x = start * width
            end_x = end * width

            # Transcribed segment fill
            painter.fillRect(
                start_x, 0, end_x - start_x, height, self.transcribed_color
            )

            # Transcribed segment borders
            pen = QPen(self.transcribed_border_color, 2, Qt.DashLine)
            painter.setPen(pen)
            painter.drawLine(start_x, 0, start_x, height)
            painter.drawLine(end_x, 0, end_x, height)

        # Draw selection range
        start_x = self.selected_range[0] * width
        end_x = self.selected_range[1] * width

        # Selection fill
        painter.fillRect(start_x, 0, end_x - start_x, height, self.selection_color)

        # Selection borders
        pen = QPen(self.selection_border_color, 2, Qt.DashLine)
        painter.setPen(pen)
        painter.drawLine(start_x, 0, start_x, height)
        painter.drawLine(end_x, 0, end_x, height)

        # Draw playhead if playing
        if self.is_playing:
            playhead_x = self.playback_position * width

            # Draw triangle playhead
            painter.setBrush(QBrush(self.playhead_color))
            painter.setPen(Qt.NoPen)

            path = QPainterPath()
            path.moveTo(playhead_x, 0)
            path.lineTo(playhead_x + 10, height / 2)
            path.lineTo(playhead_x, height)
            path.lineTo(playhead_x - 10, height / 2)
            path.closeSubpath()

            painter.drawPath(path)


class AudioWaveVisualizer(QWidget):
    """Audio Wave Visualizer component for PySide6 with audio file support"""

    # Signals for external components to connect to
    playStateChanged = Signal(bool)  # Emitted when play/pause state changes
    selectionChanged = Signal(float, float)  # Emitted when selection range changes
    audioLoaded = Signal(
        str, float, int
    )  # Emitted when audio is loaded (file_path, duration, sample_rate)
    audioSelected = Signal(
        object
    )  # Emitted when selection changes (selected audio data)
    audioSaved = Signal(str)  # Emitted when audio is saved to file

    def __init__(self, parent=None):
        super().__init__(parent)

        # Default appearance properties
        self._bg_color = QColor(30, 41, 59)  # #1e293b
        self._panel_color = QColor(30, 41, 59, 200)  # #1e293b with alpha
        self._text_color = QColor(255, 255, 255)  # white
        self._secondary_text_color = QColor(148, 163, 184)  # #94a3b8
        self._button_play_color = QColor(59, 130, 246)  # #3b82f6
        self._button_play_hover_color = QColor(37, 99, 235)  # #2563eb
        self._button_stop_color = QColor(239, 68, 68)  # #ef4444
        self._button_stop_hover_color = QColor(220, 38, 38)  # #dc2626
        self._button_reset_color = QColor(71, 85, 105)  # #475569
        self._button_reset_hover_color = QColor(51, 65, 85)  # #334155
        self._button_save_color = QColor(16, 185, 129)  # #10b981
        self._button_save_hover_color = QColor(5, 150, 105)  # #059669
        self._slider_color = QColor(71, 85, 105)  # #475569
        self._slider_handle_color = QColor(96, 165, 250)  # #60a5fa

        # Audio properties
        self.audio_file_path = ""
        self.audio_duration = 0.0  # in seconds
        self.sample_rate = 44100
        self.raw_audio_data = None  # Full audio data as numpy array
        self.audio_segment = None  # Full audio as pydub AudioSegment

        # State properties
        self._selected_range = (0.0, 1.0)
        self._is_playing = False

        # Initialize UI
        self.init_ui()

        # Generate mock audio data initially
        self.generate_waveform_data()

    def init_ui(self):
        """Initialize the UI components"""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # Title
        title_label = QLabel("Audio Wave Visualizer")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: white;")
        main_layout.addWidget(title_label)

        # Description
        desc_label = QLabel("Visualize, select, and process portions of audio waveform")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("color: #94a3b8;")
        main_layout.addWidget(desc_label)

        # Panel for controls and visualization
        panel = QFrame()
        panel_layout = QVBoxLayout(panel)
        panel_layout.setContentsMargins(15, 15, 15, 15)
        panel_layout.setSpacing(15)

        # Set panel appearance
        panel.setStyleSheet(
            f"""
            QFrame {{
                background-color: {self._panel_color.name()};
                border-radius: 10px;
            }}
        """
        )

        # Buttons layout
        buttons_layout = QHBoxLayout()

        # Load Audio button
        self.load_button = QPushButton("Load Audio")
        self.load_button.clicked.connect(self.load_audio_file)
        self.load_button.setStyleSheet(
            f"""
            QPushButton {{
                background-color: {self._button_play_color.name()};
                color: white;
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self._button_play_hover_color.name()};
            }}
        """
        )
        buttons_layout.addWidget(self.load_button)

        # Play/Stop button
        self.play_button = QPushButton("Play")
        self.play_button.clicked.connect(self.toggle_play)
        self.update_play_button_style()
        buttons_layout.addWidget(self.play_button)

        # Reset button
        self.reset_button = QPushButton("Reset Selection")
        self.reset_button.clicked.connect(self.reset_selection)
        self.reset_button.setStyleSheet(
            f"""
            QPushButton {{
                background-color: {self._button_reset_color.name()};
                color: white;
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self._button_reset_hover_color.name()};
            }}
        """
        )
        buttons_layout.addWidget(self.reset_button)

        # Save Selection button
        self.save_button = QPushButton("Save Selection")
        self.save_button.clicked.connect(self.save_selection)
        self.save_button.setEnabled(False)  # Disabled until audio is loaded
        self.save_button.setStyleSheet(
            f"""
            QPushButton {{
                background-color: {self._button_save_color.name()};
                color: white;
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self._button_save_hover_color.name()};
            }}
            QPushButton:disabled {{
                background-color: {self._button_reset_color.name()};
                color: #94a3b8;
            }}
        """
        )
        buttons_layout.addWidget(self.save_button)

        buttons_layout.addStretch()
        panel_layout.addLayout(buttons_layout)

        # Waveform canvas
        self.waveform_canvas = WaveformCanvas()
        self.waveform_canvas.positionChanged.connect(self.on_position_changed)
        panel_layout.addWidget(self.waveform_canvas)

        # File info
        self.file_info_label = QLabel("No audio file loaded")
        self.file_info_label.setStyleSheet("color: #94a3b8; font-size: 12px;")
        panel_layout.addWidget(self.file_info_label)

        # Range slider (replaces the two separate sliders)
        self.range_slider = RangeSlider()
        self.range_slider.rangeChanged.connect(self.on_range_changed)
        panel_layout.addWidget(self.range_slider)

        # Selection info
        info_frame = QFrame()
        info_frame.setStyleSheet(
            f"""
            QFrame {{
                background-color: {self._slider_color.name()};
                border-radius: 8px;
            }}
        """
        )

        info_layout = QGridLayout(info_frame)
        info_layout.setContentsMargins(10, 10, 10, 10)

        # Info labels
        self.duration_label = QLabel("Duration: 100.0%")
        self.start_info_label = QLabel("Start: 0.0%")
        self.end_info_label = QLabel("End: 100.0%")
        self.status_label = QLabel("Status: Paused")
        self.status_label.setStyleSheet("color: #94a3b8;")

        info_layout.addWidget(self.duration_label, 0, 0)
        info_layout.addWidget(self.start_info_label, 0, 1)
        info_layout.addWidget(self.end_info_label, 1, 0)
        info_layout.addWidget(self.status_label, 1, 1)

        panel_layout.addWidget(info_frame)
        main_layout.addWidget(panel)

        # Set the main layout
        self.setLayout(main_layout)

    def style_slider(self, slider):
        """Apply custom styling to a slider"""
        slider.setStyleSheet(
            f"""
            QSlider::groove:horizontal {{
                height: 8px;
                background: {self._slider_color.name()};
                border-radius: 4px;
            }}
            
            QSlider::handle:horizontal {{
                background: {self._slider_handle_color.name()};
                border: 2px solid {self._bg_color.name()};
                width: 20px;
                height: 20px;
                margin: -6px 0;
                border-radius: 10px;
            }}
        """
        )

    def generate_waveform_data(self):
        """Generate mock audio data (simulating a waveform)"""
        points = 500
        data = []

        for i in range(points):
            # Create a more complex waveform with multiple frequencies
            time = i / points
            wave1 = math.sin(time * math.pi * 4) * 0.3
            wave2 = math.sin(time * math.pi * 8) * 0.2
            wave3 = math.sin(time * math.pi * 16) * 0.1
            noise = (random.random() - 0.5) * 0.05
            data.append(wave1 + wave2 + wave3 + noise)

        self.waveform_canvas.set_audio_data(data, 3.0)  # 3 seconds duration

    def load_audio_file(self):
        """Open file dialog to load an audio file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Open Audio File",
            "",
            "Audio Files (*.mp3 *.wav *.ogg *.flac *.aac);;All Files (*)",
        )

        if file_path:
            self.audio_file_path = file_path
            self.file_info_label.setText(f"Loading: {file_path.split('/')[-1]}...")

            # Create and start the audio loader thread
            self.loader_thread = AudioLoaderThread(file_path, self)
            self.loader_thread.audioLoaded.connect(self.on_audio_loaded)
            self.loader_thread.errorOccurred.connect(self.on_audio_error)
            self.loader_thread.start()

    def on_audio_loaded(self, audio_data, duration, sample_rate):
        """Handle loaded audio data"""
        # Store the visualization data
        self.waveform_canvas.set_audio_data(audio_data, duration)

        # Load the full audio for processing
        try:
            if PYDUB_AVAILABLE:
                self.audio_segment = AudioSegment.from_file(self.audio_file_path)
                self.raw_audio_data = np.array(
                    self.audio_segment.get_array_of_samples()
                )

                # Normalize to [-1.0, 1.0]
                if self.audio_segment.sample_width == 2:
                    self.raw_audio_data = self.raw_audio_data / 32768.0
                elif self.audio_segment.sample_width == 4:
                    self.raw_audio_data = self.raw_audio_data / 2147483648.0

                # Convert to mono if needed
                if self.audio_segment.channels > 1:
                    self.raw_audio_data = self.raw_audio_data.reshape(
                        (-1, self.audio_segment.channels)
                    )
                    self.raw_audio_data = np.mean(self.raw_audio_data, axis=1)

                self.audio_duration = len(self.audio_segment) / 1000.0
                self.sample_rate = self.audio_segment.frame_rate

            elif SCIPY_AVAILABLE and self.audio_file_path.lower().endswith(".wav"):
                sample_rate, samples = wavfile.read(self.audio_file_path)

                # Convert to mono if needed
                if len(samples.shape) > 1:
                    samples = np.mean(samples, axis=1)

                # Normalize to [-1.0, 1.0]
                if samples.dtype == np.int16:
                    samples = samples / 32768.0
                elif samples.dtype == np.int32:
                    samples = samples / 2147483648.0
                elif samples.dtype == np.uint8:
                    samples = (samples - 128) / 128.0

                self.raw_audio_data = samples
                self.audio_duration = len(samples) / sample_rate
                self.sample_rate = sample_rate

                # Create a mock audio segment for processing
                self.audio_segment = None  # We'll handle saving differently

            else:
                self.file_info_label.setText(
                    "Error: No audio processing library available"
                )
                return

            # Update file info
            file_name = self.audio_file_path.split("/")[-1]
            self.file_info_label.setText(
                f"Loaded: {file_name} ({self.audio_duration:.2f}s, {self.sample_rate}Hz)"
            )

            # Enable save button
            self.save_button.setEnabled(True)

            # Reset selection
            self.reset_selection()

            # Emit signal
            self.audioLoaded.emit(
                self.audio_file_path, self.audio_duration, self.sample_rate
            )

        except Exception as e:
            self.file_info_label.setText(f"Error processing audio: {str(e)}")

    def on_audio_error(self, error_message):
        """Handle audio loading errors"""
        self.file_info_label.setText(f"Error: {error_message}")

    def save_selection(self):
        """Save the selected portion of the audio to a file"""
        if self.raw_audio_data is None:
            QMessageBox.warning(self, "No Audio", "No audio file loaded.")
            return

        # Get the selection range
        start, end = self._selected_range

        # Calculate the sample indices
        start_idx = int(start * len(self.raw_audio_data))
        end_idx = int(end * len(self.raw_audio_data))

        # Get the selected audio data
        selected_data = self.raw_audio_data[start_idx:end_idx]

        # Ask for save location
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save Audio Selection",
            "",
            "WAV Files (*.wav);;MP3 Files (*.mp3);;All Files (*)",
        )

        if not file_path:
            return

        try:
            if PYDUB_AVAILABLE and self.audio_segment is not None:
                # Calculate the time range in milliseconds
                start_ms = int(start * self.audio_duration * 1000)
                end_ms = int(end * self.audio_duration * 1000)

                # Extract the selected portion
                selected_segment = self.audio_segment[start_ms:end_ms]

                # Export the selected portion
                if file_path.lower().endswith(".mp3"):
                    selected_segment.export(file_path, format="mp3")
                else:
                    selected_segment.export(file_path, format="wav")

            elif SCIPY_AVAILABLE and file_path.lower().endswith(".wav"):
                # Convert back to original format
                if (
                    self.raw_audio_data.dtype == np.float32
                    or self.raw_audio_data.dtype == np.float64
                ):
                    # Convert to 16-bit PCM
                    audio_data = (selected_data * 32767).astype(np.int16)
                else:
                    audio_data = selected_data

                # Save as WAV
                wavfile.write(file_path, self.sample_rate, audio_data)

            else:
                QMessageBox.warning(
                    self,
                    "Export Error",
                    "Cannot save in the selected format. Install pydub for more export options.",
                )
                return

            # Show success message
            QMessageBox.information(
                self, "Success", f"Audio selection saved to:\n{file_path}"
            )

            # Emit signal
            self.audioSaved.emit(file_path)

        except Exception as e:
            QMessageBox.critical(
                self, "Export Error", f"Error saving audio file:\n{str(e)}"
            )

    def get_selected_audio(self):
        """Get the selected portion of the audio as a numpy array"""
        if self.raw_audio_data is None:
            return None

        start_idx = int(self._selected_range[0] * len(self.raw_audio_data))
        end_idx = int(self._selected_range[1] * len(self.raw_audio_data))

        return self.raw_audio_data[start_idx:end_idx]

    def get_selected_audio_info(self):
        """Get information about the selected audio portion"""
        if self.raw_audio_data is None:
            return None

        start_time = self._selected_range[0] * self.audio_duration
        end_time = self._selected_range[1] * self.audio_duration
        duration = end_time - start_time
        start_sample = int(self._selected_range[0] * len(self.raw_audio_data))
        end_sample = int(self._selected_range[1] * len(self.raw_audio_data))

        return {
            "start_time": start_time,
            "end_time": end_time,
            "duration": duration,
            "start_sample": start_sample,
            "end_sample": end_sample,
            "sample_rate": self.sample_rate,
        }

    def toggle_play(self):
        """Toggle play/pause state"""
        self._is_playing = not self._is_playing
        self.waveform_canvas.set_playing(self._is_playing)
        self.update_play_button_style()
        self.update_status_label()
        self.playStateChanged.emit(self._is_playing)

    def reset_selection(self):
        """Reset selection to full range"""
        self._selected_range = (0.0, 1.0)
        self.range_slider.setValues(0.0, 1.0)
        self.waveform_canvas.set_selected_range(0.0, 1.0)
        self.update_selection_info()
        self.selectionChanged.emit(0.0, 1.0)

        # Emit selected audio signal
        selected_audio = self.get_selected_audio()
        if selected_audio is not None:
            self.audioSelected.emit(selected_audio)

    def on_range_changed(self, start, end):
        """Handle range slider change"""
        # Convert from time values to normalized range if needed
        if self.audio_duration > 0:
            start_norm = start / self.audio_duration
            end_norm = end / self.audio_duration
        else:
            start_norm = start
            end_norm = end

        self._selected_range = (start_norm, end_norm)
        self.waveform_canvas.set_selected_range(start_norm, end_norm)

        # Update spin boxes
        self.start_time_spin.setValue(start)
        self.end_time_spin.setValue(end)

        self.update_selection_info()
        self.selectionChanged.emit(start, end)

        # Emit selected audio signal
        selected_audio = self.get_selected_audio()
        if selected_audio is not None:
            self.audioSelected.emit(selected_audio)

    @Slot()
    def on_selection_changed(self):
        """Handle selection change from spin boxes."""
        start_time = self.start_time_spin.value()
        end_time = self.end_time_spin.value()

        # Update range slider
        self.range_slider.setValues(start_time, end_time)

        # Update waveform selection
        if self.audio_duration > 0:
            start_norm = start_time / self.audio_duration
            end_norm = end_time / self.audio_duration
            self.waveform_canvas.set_selected_range(start_norm, end_norm)

        self.update_transcription_button_state()

    def on_position_changed(self, position):
        """Handle playback position changes"""
        # This could be used to sync with external audio playback
        pass

    def load_existing_transcriptions(self):
        """Load existing transcriptions for the current file."""
        if not self.current_audio_info:
            return

        segments = self.data_manager.get_transcription_segments(
            self.current_audio_info.file_hash
        )

        # Update waveform with transcription segments
        for segment in segments:
            # Convert time to normalized range (0.0 to 1.0)
            start_norm = segment.start_time / self.audio_duration
            end_norm = segment.end_time / self.audio_duration
            self.waveform_canvas.add_transcribed_segment(start_norm, end_norm, segment.text)

        # Update transcription list
        self.transcription_list.clear()
        for segment in segments:
            item_text = (
                f"[{segment.start_time:.1f}s - {segment.end_time:.1f}s] {segment.text}"
            )
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, segment)
            self.transcription_list.addItem(item)

    def update_model_list(self):
        """Update the model selection combo box."""
        models = self.transcription_manager.scan_for_models()
        self.model_combo.clear()

        if models:
            for model_path in models:
                path_obj = Path(model_path)
                model_name = path_obj.name
                if path_obj.parent.name != "tts-models":
                    model_name = f"{path_obj.parent.name}/{model_name}"
                self.model_combo.addItem(model_name, model_path)
        else:
            self.model_combo.addItem("No models found", "")

    @Slot()
    def load_selected_model(self):
        """Load the selected transcription model."""
        if self.model_combo.currentIndex() < 0:
            return

        model_path = self.model_combo.currentData()
        if not model_path:
            QMessageBox.warning(self, "No Model", "No valid model selected.")
            return

        self.model_status_label.setText("Loading model...")
        self.load_model_button.setEnabled(False)

        success = self.transcription_manager.load_model(model_path)
        if not success:
            self.model_status_label.setText("Failed to load model")
            self.load_model_button.setEnabled(True)

    def update_transcription_button_state(self):
        """Update the transcribe button state."""
        has_model = bool(self.transcription_manager.is_model_loaded())
        has_file = self.current_audio_file is not None
        has_selection = self.end_time_spin.value() > self.start_time_spin.value()

        self.transcribe_segment_button.setEnabled(
            has_model and has_file and has_selection
        )

    @Slot()
    def transcribe_selected_segment(self):
        """Transcribe the selected audio segment."""
        if (
            not self.current_audio_file
            or not self.transcription_manager.is_model_loaded()
        ):
            return

        start_time = self.start_time_spin.value()
        end_time = self.end_time_spin.value()

        if start_time >= end_time:
            QMessageBox.warning(
                self, "Invalid Selection", "End time must be greater than start time."
            )
            return

        # Start transcription
        success = self.transcription_manager.transcribe_segment(
            self.current_audio_file, start_time, end_time
        )

        if not success:
            QMessageBox.critical(
                self, "Transcription Error", "Failed to start transcription."
            )

    # === Transcription Signal Handlers ===
    @Slot(float, float)
    def on_transcription_started(self, start_time: float, end_time: float):
        """Handle transcription started."""
        self.transcription_progress.setVisible(True)
        self.transcription_progress.setRange(0, 100)
        self.transcription_progress.setValue(0)
        self.transcribe_segment_button.setEnabled(False)

    @Slot(object)
    def on_transcription_completed(self, result: TranscriptionResult):
        """Handle transcription completed."""
        self.transcription_progress.setVisible(False)
        self.transcribe_segment_button.setEnabled(True)

        if not result.text.strip():
            QMessageBox.information(
                self, "No Speech", "No speech detected in the selected segment."
            )
            return

        # Save to database
        if self.current_audio_info:
            segment = TranscriptionSegment(
                id=None,
                audio_file_hash=self.current_audio_info.file_hash,
                start_time=result.segment.start_time,
                end_time=result.segment.end_time,
                text=result.text,
                confidence=result.confidence,
                created_at=result.timestamp,
                last_modified=result.timestamp,
            )

            segment_id = self.data_manager.save_transcription_segment(segment)
            if segment_id:
                segment.id = segment_id

                # Add to UI
                item_text = f"[{segment.start_time:.1f}s - {segment.end_time:.1f}s] {segment.text}"
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, segment)
                self.transcription_list.addItem(item)

                # Update waveform
                self.load_existing_transcriptions()

                # Emit completion signal
                self.transcriptionCompleted.emit(result)

    @Slot(str)
    def on_transcription_error(self, error_message: str):
        """Handle transcription error."""
        self.transcription_progress.setVisible(False)
        self.transcribe_segment_button.setEnabled(True)
        QMessageBox.critical(self, "Transcription Error", error_message)

    @Slot(float)
    def on_transcription_progress(self, progress: float):
        """Handle transcription progress update."""
        self.transcription_progress.setValue(int(progress))

    @Slot(str)
    def on_model_loaded(self, model_path: str):
        """Handle model loaded successfully."""
        model_name = Path(model_path).name
        self.model_status_label.setText(f"Model loaded: {model_name}")
        self.load_model_button.setEnabled(True)
        self.update_transcription_button_state()

    @Slot(str)
    def on_model_load_error(self, error_message: str):
        """Handle model load error."""
        self.model_status_label.setText("Model load failed")
        self.load_model_button.setEnabled(True)
        QMessageBox.critical(self, "Model Load Error", error_message)

    @Slot()
    def clear_transcriptions(self):
        """Clear all transcriptions."""
        if self.transcription_list.count() == 0:
            return

        reply = QMessageBox.question(
            self,
            "Clear Transcriptions",
            "Are you sure you want to clear all transcriptions? This cannot be undone.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No,
        )

        if reply == QMessageBox.Yes:
            # Clear from database
            if self.current_audio_info:
                segments = self.data_manager.get_transcription_segments(
                    self.current_audio_info.file_hash
                )
                for segment in segments:
                    if segment.id:
                        self.data_manager.delete_transcription_segment(segment.id)

            # Clear from UI
            self.transcription_list.clear()
            # Clear transcribed segments from waveform
            self.waveform_canvas.transcribed_segments = []
            self.waveform_canvas.update()

    @Slot()
    def export_transcriptions(self):
        """Export transcriptions to a text file."""
        if self.transcription_list.count() == 0:
            QMessageBox.information(
                self, "No Transcriptions", "No transcriptions to export."
            )
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export Transcriptions", "", "Text Files (*.txt);;All Files (*)"
        )

        if file_path:
            try:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(
                        f"Transcriptions for: {Path(self.current_audio_file).name}\n"
                    )
                    f.write("=" * 50 + "\n\n")

                    for i in range(self.transcription_list.count()):
                        item = self.transcription_list.item(i)
                        segment = item.data(Qt.UserRole)

                        f.write(
                            f"[{segment.start_time:.1f}s - {segment.end_time:.1f}s]\n"
                        )
                        f.write(f"{segment.text}\n")
                        f.write(f"Confidence: {segment.confidence:.2f}\n\n")

                QMessageBox.information(
                    self, "Export Complete", f"Transcriptions exported to:\n{file_path}"
                )

            except Exception as e:
                QMessageBox.critical(
                    self, "Export Error", f"Failed to export transcriptions: {e}"
                )

    def update_play_button_style(self):
        """Update the play/pause button style"""
        if self._is_playing:
            self.play_button.setText("Stop")
            self.play_button.setStyleSheet(
                f"""
                QPushButton {{
                    background-color: {self._button_stop_color.name()};
                    color: white;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: {self._button_stop_hover_color.name()};
                }}
            """
            )
        else:
            self.play_button.setText("Play")
            self.play_button.setStyleSheet(
                f"""
                QPushButton {{
                    background-color: {self._button_play_color.name()};
                    color: white;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: {self._button_play_hover_color.name()};
                }}
            """
            )

    def update_selection_info(self):
        """Update the selection information display"""
        start, end = self._selected_range
        duration = end - start

        self.start_info_label.setText(f"Start: {start * 100:.1f}%")
        self.end_info_label.setText(f"End: {end * 100:.1f}%")
        self.duration_label.setText(f"Duration: {duration * 100:.1f}%")

    def update_status_label(self):
        """Update the status label"""
        if self._is_playing:
            self.status_label.setText("Status: Playing")
            self.status_label.setStyleSheet("color: #4ade80;")  # green-400
        else:
            self.status_label.setText("Status: Paused")
            self.status_label.setStyleSheet("color: #94a3b8;")  # slate-400

    # Property getters and setters for customization
    def get_bg_color(self):
        return self._bg_color

    def set_bg_color(self, color):
        self._bg_color = color
        self.setStyleSheet(f"background-color: {color.name()};")

    bg_color = Property(QColor, get_bg_color, set_bg_color)

    def get_waveform_color(self):
        return self.waveform_canvas.waveform_color

    def set_waveform_color(self, color):
        self.waveform_canvas.waveform_color = color
        self.waveform_canvas.update()

    waveform_color = Property(QColor, get_waveform_color, set_waveform_color)

    def get_selection_color(self):
        return self.waveform_canvas.selection_color

    def set_selection_color(self, color):
        self.waveform_canvas.selection_color = color
        self.waveform_canvas.update()

    selection_color = Property(QColor, get_selection_color, set_selection_color)

    def get_playhead_color(self):
        return self.waveform_canvas.playhead_color

    def set_playhead_color(self, color):
        self.waveform_canvas.playhead_color = color
        self.waveform_canvas.update()

    playhead_color = Property(QColor, get_playhead_color, set_playhead_color)

    def get_selected_range(self):
        return self._selected_range

    def set_selected_range(self, range_tuple):
        start, end = range_tuple
        self._selected_range = (max(0.0, min(1.0, start)), max(0.0, min(1.0, end)))
        self.range_slider.setValues(self._selected_range[0], self._selected_range[1])
        self.waveform_canvas.set_selected_range(
            self._selected_range[0], self._selected_range[1]
        )
        self.update_selection_info()

    selected_range = Property(tuple, get_selected_range, set_selected_range)

    def get_is_playing(self):
        return self._is_playing

    def set_is_playing(self, playing):
        if self._is_playing != playing:
            self.toggle_play()

    is_playing = Property(bool, get_is_playing, set_is_playing)

    def set_audio_data(self, data, duration=1.0):
        """Set the audio data to visualize"""
        self.waveform_canvas.set_audio_data(data, duration)

    def add_transcribed_segment(self, start, end, text):
        """Add a transcribed segment to the visualization"""
        self.waveform_canvas.add_transcribed_segment(start, end, text)

    def remove_transcribed_segment(self, index):
        """Remove a transcribed segment by index"""
        self.waveform_canvas.remove_transcribed_segment(index)

    def get_transcribed_segments(self):
        """Get all transcribed segments"""
        return self.waveform_canvas.get_transcribed_segments()

    def is_segment_transcribed(self, start, end):
        """Check if a segment overlaps with any transcribed segment"""
        return self.waveform_canvas.is_segment_transcribed(start, end)


class TranscriptionService(QThread):
    """Mock transcription service that simulates audio transcription"""

    transcriptionComplete = Signal(
        str
    )  # Emitted when transcription is complete with the text

    def __init__(self, audio_data, parent=None):
        super().__init__(parent)
        self.audio_data = audio_data

    def run(self):
        """Simulate transcription processing"""
        # Simulate processing time
        self.msleep(1500)

        # Generate a mock transcription
        if self.audio_data is not None and len(self.audio_data) > 0:
            # Create a mock transcription based on the audio length
            length = len(self.audio_data)
            if length < 1000:
                text = "Short audio segment detected. Transcription: This is a brief audio clip."
            elif length < 5000:
                text = "Medium audio segment detected. Transcription: This audio contains several sentences of spoken content."
            else:
                text = "Long audio segment detected. Transcription: This audio contains a lengthy passage with multiple speakers and complex content."
        else:
            text = "No audio data available for transcription."

        # Emit the transcription result
        self.transcriptionComplete.emit(text)


class TranscriptionItemDelegate(QStyledItemDelegate):
    """Delegate for rendering transcription items with rich text"""

    def __init__(self, parent=None):
        super().__init__(parent)

    def paint(self, painter, option, index):
        """Custom paint event for transcription items"""
        # Get the data
        text = index.data(Qt.DisplayRole)
        time_range = index.data(Qt.UserRole)

        # Draw the background
        if option.state & QStyle.State_Selected:
            painter.fillRect(option.rect, option.palette.highlight())
        else:
            painter.fillRect(option.rect, option.palette.base())

        # Set up text rendering
        painter.save()

        # Draw the time range
        time_text = f"Time: {time_range[0]:.2f}s - {time_range[1]:.2f}s"
        time_font = QFont("Arial", 10, QFont.Bold)
        painter.setFont(time_font)
        painter.setPen(QColor(96, 165, 250))  # Blue color for time

        time_rect = option.rect.adjusted(10, 5, -10, 0)
        painter.drawText(time_rect, Qt.AlignLeft, time_text)

        # Draw the transcription text
        text_font = QFont("Arial", 9)
        painter.setFont(text_font)
        painter.setPen(QColor(255, 255, 255))  # White color for text

        # Calculate text rectangle (below time)
        text_rect = option.rect.adjusted(10, 25, -10, -5)

        # Use QTextDocument for word wrapping
        doc = QTextDocument()
        doc.setPlainText(text)
        doc.setTextWidth(text_rect.width())

        # Draw the text
        painter.translate(text_rect.topLeft())
        doc.drawContents(painter)
        painter.restore()

    def sizeHint(self, option, index):
        """Calculate the size needed for the item"""
        # Get the text
        text = index.data(Qt.DisplayRole)

        # Create a QTextDocument to calculate the height
        doc = QTextDocument()
        doc.setPlainText(text)
        doc.setTextWidth(option.rect.width() - 20)  # Account for margins

        # Return the size (height based on text, width based on option)
        return QSize(
            option.rect.width(), doc.size().height() + 40
        )  # Add extra space for time label


class AudioTranscriptionApp(QMainWindow):
    """Main application window for audio transcription"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("Audio Transcription App")
        self.resize(1200, 800)

        # Set application style
        self.setStyleSheet(
            """
            QMainWindow, QWidget {
                background-color: #1e293b;
                color: white;
            }
            QLabel {
                color: white;
            }
            QPushButton {
                background-color: #3b82f6;
                color: white;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2563eb;
            }
            QPushButton:disabled {
                background-color: #475569;
                color: #94a3b8;
            }
            QListWidget {
                background-color: #334155;
                border-radius: 8px;
                border: 1px solid #475569;
            }
            QScrollArea {
                background-color: #1e293b;
                border: none;
            }
        """
        )

        # Initialize UI
        self.init_ui()

        # Transcription service
        self.transcription_service = None
        self.transcribing = False

    def init_ui(self):
        """Initialize the UI components"""
        # Create a central widget with a scroll area
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Create a container widget for the scroll area
        container_widget = QWidget()
        container_layout = QVBoxLayout(container_widget)
        container_layout.setContentsMargins(20, 20, 20, 20)
        container_layout.setSpacing(20)

        # Add the container widget to the scroll area
        self.scroll_area.setWidget(container_widget)
        self.setCentralWidget(self.scroll_area)

        # Create a splitter for resizable sections
        splitter = QSplitter(Qt.Vertical)
        container_layout.addWidget(splitter)

        # Top section: Audio Wave Visualizer
        self.waveform_visualizer = AudioWaveVisualizer()
        splitter.addWidget(self.waveform_visualizer)

        # Connect signals
        self.waveform_visualizer.audioLoaded.connect(self.on_audio_loaded)
        self.waveform_visualizer.selectionChanged.connect(self.on_selection_changed)
        self.waveform_visualizer.audioSelected.connect(self.on_audio_selected)

        # Middle section: Transcription controls
        controls_frame = QFrame()
        controls_layout = QHBoxLayout(controls_frame)
        controls_layout.setContentsMargins(10, 10, 10, 10)

        # Transcribe button
        self.transcribe_button = QPushButton("Transcribe Selection")
        self.transcribe_button.clicked.connect(self.transcribe_selection)
        self.transcribe_button.setEnabled(False)  # Disabled until audio is loaded
        controls_layout.addWidget(self.transcribe_button)

        # Status label
        self.status_label = QLabel("Ready")
        controls_layout.addWidget(self.status_label)
        controls_layout.addStretch()

        splitter.addWidget(controls_frame)

        # Bottom section: Transcriptions list
        transcriptions_frame = QFrame()
        transcriptions_layout = QVBoxLayout(transcriptions_frame)
        transcriptions_layout.setContentsMargins(10, 10, 10, 10)

        # Title for transcriptions
        transcriptions_title = QLabel("Transcribed Segments")
        transcriptions_title.setStyleSheet("font-size: 16px; font-weight: bold;")
        transcriptions_layout.addWidget(transcriptions_title)

        # Create a scroll area for the transcriptions list
        list_scroll_area = QScrollArea()
        list_scroll_area.setWidgetResizable(True)
        list_scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        list_scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Create the transcriptions list widget
        self.transcriptions_list = QListWidget()
        self.transcriptions_list.setItemDelegate(TranscriptionItemDelegate())
        self.transcriptions_list.setSelectionMode(QAbstractItemView.SingleSelection)
        self.transcriptions_list.setAlternatingRowColors(True)

        # Set custom stylesheet for the list
        self.transcriptions_list.setStyleSheet(
            """
            QListWidget {
                background-color: #334155;
                border-radius: 8px;
                border: 1px solid #475569;
            }
            QListWidget::item {
                border-bottom: 1px solid #475569;
                padding: 5px;
            }
            QListWidget::item:selected {
                background-color: #1e40af;
            }
        """
        )

        list_scroll_area.setWidget(self.transcriptions_list)
        transcriptions_layout.addWidget(list_scroll_area)

        # Add a button to remove selected transcription
        remove_button_layout = QHBoxLayout()
        remove_button_layout.addStretch()

        self.remove_button = QPushButton("Remove Selected Transcription")
        self.remove_button.clicked.connect(self.remove_selected_transcription)
        self.remove_button.setEnabled(
            False
        )  # Disabled until a transcription is selected
        remove_button_layout.addWidget(self.remove_button)

        transcriptions_layout.addLayout(remove_button_layout)
        splitter.addWidget(transcriptions_frame)

        # Set the initial sizes of the splitter
        splitter.setSizes([400, 100, 300])

        # Initialize data
        self.transcriptions = (
            []
        )  # List of dicts: {'start': float, 'end': float, 'text': str}

    def on_audio_loaded(self, file_path, duration, sample_rate):
        """Handle audio loaded event"""
        self.transcribe_button.setEnabled(True)
        self.status_label.setText(f"Audio loaded: {file_path}")

    def on_selection_changed(self, start, end):
        """Handle selection changed event"""
        # Check if the selection overlaps with any transcribed segment
        if self.waveform_visualizer.is_segment_transcribed(start, end):
            self.transcribe_button.setEnabled(False)
            self.status_label.setText("Selection overlaps with transcribed segment")
        else:
            self.transcribe_button.setEnabled(True)
            self.status_label.setText(f"Selection: {start:.2f} - {end:.2f}")

    def on_audio_selected(self, audio_data):
        """Handle audio selected event"""
        # This could be used for preview or other purposes
        pass

    def transcribe_selection(self):
        """Transcribe the selected audio segment"""
        if self.transcribing:
            return

        # Get the current selection
        start, end = self.waveform_visualizer.selected_range

        # Check if the selection overlaps with any transcribed segment
        if self.waveform_visualizer.is_segment_transcribed(start, end):
            QMessageBox.warning(
                self,
                "Overlap",
                "The selected segment overlaps with a transcribed segment.",
            )
            return

        # Get the selected audio data
        audio_data = self.waveform_visualizer.get_selected_audio()

        if audio_data is None:
            QMessageBox.warning(
                self, "No Audio", "No audio data available for transcription."
            )
            return

        # Update UI
        self.transcribing = True
        self.transcribe_button.setEnabled(False)
        self.status_label.setText("Transcribing...")

        # Create and start the transcription service
        self.transcription_service = TranscriptionService(audio_data, self)
        self.transcription_service.transcriptionComplete.connect(
            self.on_transcription_complete
        )
        self.transcription_service.finished.connect(self.on_transcription_finished)
        self.transcription_service.start()

    def on_transcription_complete(self, text):
        """Handle transcription complete event"""
        # Get the current selection
        start, end = self.waveform_visualizer.selected_range

        # Add the transcribed segment to the waveform
        self.waveform_visualizer.add_transcribed_segment(start, end, text)

        # Add to our transcriptions list
        transcription = {"start": start, "end": end, "text": text}
        self.transcriptions.append(transcription)

        # Add to the list widget
        self.add_transcription_to_list(transcription)

        # Update UI
        self.status_label.setText("Transcription complete")

    def on_transcription_finished(self):
        """Handle transcription service finished"""
        self.transcribing = False
        self.transcribe_button.setEnabled(True)
        self.transcription_service = None

    def add_transcription_to_list(self, transcription):
        """Add a transcription to the list widget"""
        # Create a list item
        item = QListWidgetItem()

        # Set the display text
        item.setText(transcription["text"])

        # Set the user data (time range)
        item.setData(Qt.UserRole, (transcription["start"], transcription["end"]))

        # Add to the list
        self.transcriptions_list.addItem(item)

        # Enable the remove button
        self.remove_button.setEnabled(True)

    def remove_selected_transcription(self):
        """Remove the selected transcription"""
        # Get the selected item
        selected_items = self.transcriptions_list.selectedItems()
        if not selected_items:
            return

        selected_item = selected_items[0]
        row = self.transcriptions_list.row(selected_item)

        # Get the transcription data
        start, end = selected_item.data(Qt.UserRole)

        # Remove from the waveform
        self.waveform_visualizer.remove_transcribed_segment(row)

        # Remove from our transcriptions list
        if row < len(self.transcriptions):
            self.transcriptions.pop(row)

        # Remove from the list widget
        self.transcriptions_list.takeItem(row)

        # Update UI
        self.status_label.setText("Transcription removed")

        # Disable the remove button if no items left
        if self.transcriptions_list.count() == 0:
            self.remove_button.setEnabled(False)


# Example usage
if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Create the audio transcription app
    window = AudioTranscriptionApp()
    window.show()

    sys.exit(app.exec())
