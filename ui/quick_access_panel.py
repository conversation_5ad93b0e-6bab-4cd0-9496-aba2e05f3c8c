# -*- coding: utf-8 -*-
"""
Quick Access Panel

This module provides a quick access panel system that can be docked to screen edges
and provides immediate access to translation and audio features without multiple
navigation steps.

Features:
- Edge-docked panel that slides in/out
- Quick access to all features
- Visual status indicators
- Keyboard shortcuts
- Minimal screen real estate usage
"""

import os
import sys
from typing import Optional, Dict, Any
from enum import Enum

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame,
    QApplication, QGraphicsDropShadowEffect, QSizePolicy
)
from PySide6.QtCore import (
    Qt, Signal, Slot, QTimer, QPropertyAnimation, QEasingCurve, 
    QRect, QPoint, QSize
)
from PySide6.QtGui import QFont, QIcon, QColor, QKeySequence, QShortcut

from ui.widgets import StyledButton

class PanelPosition(Enum):
    """Panel docking positions."""
    LEFT = "left"
    RIGHT = "right"
    TOP = "top"
    BOTTOM = "bottom"

class QuickAccessPanel(QWidget):
    """
    A quick access panel that can be docked to screen edges and provides
    immediate access to translation and audio features.
    """
    
    # Signals
    feature_requested = Signal(str)  # feature_name
    service_toggle_requested = Signal()
    dashboard_requested = Signal()
    
    def __init__(self, position: PanelPosition = PanelPosition.RIGHT):
        super().__init__()
        
        self.position = position
        self.is_expanded = False
        self.collapsed_size = 60
        self.expanded_size = 280
        
        # Animation
        self.slide_animation = None
        
        self.setup_ui()
        self.setup_styling()
        self.setup_animations()
        self.setup_keyboard_shortcuts()
        self.position_panel()
        
    def setup_ui(self):
        """Set up the panel UI."""
        self.setWindowTitle("Quick Access Panel")
        self.setWindowFlags(
            Qt.WindowStaysOnTopHint | 
            Qt.FramelessWindowHint | 
            Qt.Tool
        )
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # Main layout
        if self.position in [PanelPosition.LEFT, PanelPosition.RIGHT]:
            main_layout = QVBoxLayout(self)
            self.setFixedWidth(self.collapsed_size)
            self.setMinimumHeight(400)
        else:
            main_layout = QHBoxLayout(self)
            self.setFixedHeight(self.collapsed_size)
            self.setMinimumWidth(400)
            
        main_layout.setContentsMargins(8, 8, 8, 8)
        main_layout.setSpacing(8)
        
        # Panel container
        self.panel_container = QFrame()
        self.panel_container.setObjectName("QuickAccessPanelContainer")
        
        container_layout = QVBoxLayout(self.panel_container)
        container_layout.setContentsMargins(12, 12, 12, 12)
        container_layout.setSpacing(8)
        
        # Toggle button
        self.toggle_button = StyledButton("⚡", "primary")
        self.toggle_button.setObjectName("PanelToggleButton")
        self.toggle_button.setFixedSize(40, 40)
        self.toggle_button.clicked.connect(self.toggle_panel)
        container_layout.addWidget(self.toggle_button)
        
        # Service status indicator
        self.service_indicator = QLabel("●")
        self.service_indicator.setObjectName("ServiceIndicator")
        self.service_indicator.setAlignment(Qt.AlignCenter)
        self.service_indicator.setFixedHeight(20)
        container_layout.addWidget(self.service_indicator)
        
        # Feature buttons (initially hidden)
        self.feature_buttons_container = QFrame()
        self.feature_buttons_container.setObjectName("FeatureButtonsContainer")
        self.feature_buttons_container.hide()
        
        buttons_layout = QVBoxLayout(self.feature_buttons_container)
        buttons_layout.setContentsMargins(0, 0, 0, 0)
        buttons_layout.setSpacing(6)
        
        # Translation features
        self.translate_btn = self.create_feature_button("🌐", "Quick Translate", "translate")
        self.service_toggle_btn = self.create_feature_button("⚙️", "Toggle Service", "service_toggle")
        
        # Audio features
        self.record_btn = self.create_feature_button("🎙", "Record Audio", "record_audio")
        self.transcribe_btn = self.create_feature_button("📝", "Transcribe", "transcribe_audio")
        self.playback_btn = self.create_feature_button("▶️", "Playback", "playback_audio")
        
        # Dashboard and settings
        self.dashboard_btn = self.create_feature_button("📊", "Dashboard", "dashboard")
        self.settings_btn = self.create_feature_button("⚙️", "Settings", "settings")
        
        buttons_layout.addWidget(self.translate_btn)
        buttons_layout.addWidget(self.service_toggle_btn)
        buttons_layout.addWidget(QFrame())  # Separator
        buttons_layout.addWidget(self.record_btn)
        buttons_layout.addWidget(self.transcribe_btn)
        buttons_layout.addWidget(self.playback_btn)
        buttons_layout.addWidget(QFrame())  # Separator
        buttons_layout.addWidget(self.dashboard_btn)
        buttons_layout.addWidget(self.settings_btn)
        
        container_layout.addWidget(self.feature_buttons_container)
        container_layout.addStretch()
        
        main_layout.addWidget(self.panel_container)
        
    def create_feature_button(self, icon: str, tooltip: str, feature_name: str) -> QPushButton:
        """Create a feature button."""
        button = StyledButton(icon, "ghost")
        button.setObjectName("FeatureButton")
        button.setToolTip(tooltip)
        button.setFixedSize(40, 40)
        button.clicked.connect(lambda: self.on_feature_requested(feature_name))
        return button
        
    def on_feature_requested(self, feature_name: str):
        """Handle feature request."""
        if feature_name == "service_toggle":
            self.service_toggle_requested.emit()
        elif feature_name == "dashboard":
            self.dashboard_requested.emit()
        else:
            self.feature_requested.emit(feature_name)
            
    def setup_styling(self):
        """Set up panel styling."""
        self.setObjectName("QuickAccessPanel")
        
        # Add drop shadow
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 0)
        self.setGraphicsEffect(shadow)
        
    def setup_animations(self):
        """Set up panel animations."""
        self.slide_animation = QPropertyAnimation(self, b"geometry")
        self.slide_animation.setDuration(300)
        self.slide_animation.setEasingCurve(QEasingCurve.OutCubic)
        
    def setup_keyboard_shortcuts(self):
        """Set up keyboard shortcuts."""
        # Toggle panel
        self.toggle_shortcut = QShortcut(QKeySequence("Ctrl+Shift+Q"), self)
        self.toggle_shortcut.activated.connect(self.toggle_panel)
        
        # Quick translate
        self.translate_shortcut = QShortcut(QKeySequence("Ctrl+Shift+T"), self)
        self.translate_shortcut.activated.connect(lambda: self.feature_requested.emit("translate"))
        
    def position_panel(self):
        """Position the panel on the screen edge."""
        screen = QApplication.primaryScreen().geometry()
        
        if self.position == PanelPosition.RIGHT:
            x = screen.width() - self.width()
            y = (screen.height() - self.height()) // 2
        elif self.position == PanelPosition.LEFT:
            x = 0
            y = (screen.height() - self.height()) // 2
        elif self.position == PanelPosition.TOP:
            x = (screen.width() - self.width()) // 2
            y = 0
        else:  # BOTTOM
            x = (screen.width() - self.width()) // 2
            y = screen.height() - self.height()
            
        self.move(x, y)
        
    def toggle_panel(self):
        """Toggle panel expansion."""
        if self.is_expanded:
            self.collapse_panel()
        else:
            self.expand_panel()
            
    def expand_panel(self):
        """Expand the panel to show all features."""
        if self.is_expanded:
            return
            
        self.is_expanded = True
        
        # Show feature buttons
        self.feature_buttons_container.show()
        
        # Animate expansion
        current_rect = self.geometry()
        
        if self.position in [PanelPosition.LEFT, PanelPosition.RIGHT]:
            new_width = self.expanded_size
            new_height = current_rect.height()
            
            if self.position == PanelPosition.RIGHT:
                new_x = current_rect.x() - (new_width - current_rect.width())
                new_y = current_rect.y()
            else:
                new_x = current_rect.x()
                new_y = current_rect.y()
        else:
            new_width = current_rect.width()
            new_height = self.expanded_size
            
            if self.position == PanelPosition.BOTTOM:
                new_x = current_rect.x()
                new_y = current_rect.y() - (new_height - current_rect.height())
            else:
                new_x = current_rect.x()
                new_y = current_rect.y()
                
        new_rect = QRect(new_x, new_y, new_width, new_height)
        
        self.slide_animation.setStartValue(current_rect)
        self.slide_animation.setEndValue(new_rect)
        self.slide_animation.start()
        
        # Update toggle button
        self.toggle_button.setText("◀" if self.position == PanelPosition.RIGHT else "▶")
        
    def collapse_panel(self):
        """Collapse the panel to minimal size."""
        if not self.is_expanded:
            return
            
        self.is_expanded = False
        
        # Hide feature buttons
        self.feature_buttons_container.hide()
        
        # Animate collapse
        current_rect = self.geometry()
        
        if self.position in [PanelPosition.LEFT, PanelPosition.RIGHT]:
            new_width = self.collapsed_size
            new_height = current_rect.height()
            
            if self.position == PanelPosition.RIGHT:
                new_x = current_rect.x() + (current_rect.width() - new_width)
                new_y = current_rect.y()
            else:
                new_x = current_rect.x()
                new_y = current_rect.y()
        else:
            new_width = current_rect.width()
            new_height = self.collapsed_size
            
            if self.position == PanelPosition.BOTTOM:
                new_x = current_rect.x()
                new_y = current_rect.y() + (current_rect.height() - new_height)
            else:
                new_x = current_rect.x()
                new_y = current_rect.y()
                
        new_rect = QRect(new_x, new_y, new_width, new_height)
        
        self.slide_animation.setStartValue(current_rect)
        self.slide_animation.setEndValue(new_rect)
        self.slide_animation.start()
        
        # Update toggle button
        self.toggle_button.setText("⚡")
        
    def update_service_status(self, is_active: bool):
        """Update service status indicator."""
        if is_active:
            self.service_indicator.setStyleSheet("color: #22c55e;")  # Green
            self.service_indicator.setToolTip("Translation service is active")
        else:
            self.service_indicator.setStyleSheet("color: #64748b;")  # Gray
            self.service_indicator.setToolTip("Translation service is inactive")
            
    def mousePressEvent(self, event):
        """Handle mouse press for dragging."""
        if event.button() == Qt.LeftButton:
            self.drag_start_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
            
    def mouseMoveEvent(self, event):
        """Handle mouse move for dragging."""
        if event.buttons() == Qt.LeftButton and hasattr(self, 'drag_start_position'):
            self.move(event.globalPosition().toPoint() - self.drag_start_position)
            event.accept()
            
    def enterEvent(self, event):
        """Auto-expand on mouse enter."""
        if not self.is_expanded:
            QTimer.singleShot(500, self.expand_panel)  # Delay to avoid accidental expansion
            
    def leaveEvent(self, event):
        """Auto-collapse on mouse leave."""
        if self.is_expanded:
            QTimer.singleShot(1000, self.collapse_panel)  # Delay to allow interaction
