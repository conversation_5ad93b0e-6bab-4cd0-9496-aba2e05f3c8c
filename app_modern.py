#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modern Translation Application

This is the main entry point for the modern translation application with
the redesigned dashboard interface.

Features:
- Modern dashboard interface
- Always-ready translation access
- Comprehensive audio processing
- Keyboard shortcuts and power user features
- System tray integration
- Responsive design
"""

import sys
import os
from pathlib import Path

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QIcon

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.modern_dashboard import ModernDashboard

class ModernTranslationApp(QApplication):
    """Modern translation application class."""
    
    def __init__(self, argv):
        super().__init__(argv)
        
        # Set application properties
        self.setApplicationName("Translation Dashboard")
        self.setApplicationVersion("2.0.0")
        self.setOrganizationName("Translation Tools")
        self.setOrganizationDomain("translationtools.app")
        
        # Set application icon if available
        icon_path = Path("icon.svg")
        if icon_path.exists():
            self.setWindowIcon(QIcon(str(icon_path)))
        
        # Enable high DPI scaling
        self.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        self.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # Load and apply modern stylesheet
        self.load_stylesheet()
        
        # Create main dashboard
        self.dashboard = None
        self.create_dashboard()
        
        # Handle application quit
        self.aboutToQuit.connect(self.cleanup)
        
    def load_stylesheet(self):
        """Load and apply the modern stylesheet."""
        try:
            style_path = Path('style.qss')
            if style_path.exists():
                with open(style_path, 'r', encoding='utf-8') as f:
                    style = f.read()
                    self.setStyleSheet(style)
                print("Modern stylesheet loaded successfully")
            else:
                print("Warning: style.qss not found, using default styles")
        except Exception as e:
            print(f"Error loading stylesheet: {e}")
            
    def create_dashboard(self):
        """Create and show the main dashboard."""
        try:
            self.dashboard = ModernDashboard()
            
            # Show dashboard on startup (can be configured)
            self.dashboard.show()
            
            print("Modern dashboard created successfully")
            
        except Exception as e:
            print(f"Error creating dashboard: {e}")
            QMessageBox.critical(
                None, 
                "Startup Error", 
                f"Failed to create dashboard: {e}\n\nThe application will exit."
            )
            self.quit()
            
    def cleanup(self):
        """Clean up resources before quitting."""
        if self.dashboard:
            self.dashboard.cleanup()
            
def check_dependencies():
    """Check if all required dependencies are available."""
    missing_deps = []
    
    try:
        import sounddevice
    except ImportError:
        missing_deps.append("sounddevice")
        
    try:
        import soundfile
    except ImportError:
        missing_deps.append("soundfile")
        
    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")
        
    try:
        import vosk
    except ImportError:
        missing_deps.append("vosk (optional for transcription)")
        
    try:
        import webrtcvad
    except ImportError:
        missing_deps.append("webrtcvad (optional for VAD)")
        
    if missing_deps:
        print("Warning: Missing optional dependencies:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\nSome features may not be available.")
        print("Install missing dependencies with: pip install <package_name>")
        
    return len(missing_deps) == 0

def main():
    """Main application entry point."""
    print("Starting Modern Translation Application...")
    
    # Check dependencies
    check_dependencies()
    
    # Create application
    app = ModernTranslationApp(sys.argv)
    
    # Set up exception handling
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
            
        print(f"Uncaught exception: {exc_type.__name__}: {exc_value}")
        QMessageBox.critical(
            None,
            "Unexpected Error",
            f"An unexpected error occurred:\n\n{exc_type.__name__}: {exc_value}\n\n"
            "Please check the console for more details."
        )
        
    sys.excepthook = handle_exception
    
    # Show startup message
    print("Dashboard interface loaded")
    print("Available keyboard shortcuts:")
    print("  Ctrl+Shift+T - Quick translate from clipboard")
    print("  Ctrl+Shift+D - Show dashboard")
    print("  Ctrl+Shift+S - Toggle translation service")
    print("\nApplication is ready!")
    
    # Run application
    try:
        exit_code = app.exec()
        print(f"Application exited with code: {exit_code}")
        return exit_code
    except Exception as e:
        print(f"Application error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
